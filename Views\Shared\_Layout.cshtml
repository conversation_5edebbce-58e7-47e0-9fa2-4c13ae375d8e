﻿<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>@ViewData["Title"] - H<PERSON></title>

    <link rel="stylesheet" href="~/lib/bootstrap/dist/css/bootstrap.min.css" />
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700;900&display=swap">
    <link rel="stylesheet" href="~/css/site.css" asp-append-version="true" />
    <link rel="stylesheet" href="~/css/sports-theme.css" asp-append-version="true" />
    <link rel="stylesheet" href="~/css/rules.css" asp-append-version="true" />
    <link rel="stylesheet" href="~/WebQuanLyGiaiDau_NhomTD.styles.css" asp-append-version="true" />
</head>
<body>

    <header>
        <nav class="navbar navbar-expand-lg sports-navbar sticky-top">
            <div class="container">
                <a class="navbar-brand" asp-area="" asp-controller="Home" asp-action="Index">
                    <i class="bi bi-trophy-fill"></i>Hệ Thống Giải Đấu
                </a>

                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target=".navbar-collapse"
                        aria-controls="navbarSupportedContent" aria-expanded="false" aria-label="Toggle navigation">
                    <span class="navbar-toggler-icon"></span>
                </button>

                <div class="navbar-collapse collapse d-lg-inline-flex justify-content-between">
                    <ul class="navbar-nav me-auto mb-2 mb-lg-0">
                        <!-- Trang chủ luôn hiển thị cho tất cả người dùng -->
                        <li class="nav-item">
                            <a class="nav-link" asp-controller="Home" asp-action="Index">
                                <i class="bi bi-house-door"></i> Trang chủ
                            </a>
                        </li>

                        <!-- Tin tức thể thao luôn hiển thị cho tất cả người dùng -->
                        <li class="nav-item">
                            <a class="nav-link" asp-controller="News" asp-action="Index">
                                <i class="bi bi-newspaper"></i> Tin tức thể thao
                            </a>
                        </li>

                        <!-- Luật thi đấu luôn hiển thị cho tất cả người dùng -->
                        <li class="nav-item">
                            <a class="nav-link" asp-controller="Rules" asp-action="Index">
                                <i class="bi bi-book"></i> Luật thi đấu
                            </a>
                        </li>

                        @if (User.Identity != null && User.Identity.IsAuthenticated)
                        {
                            <!-- Môn thể thao - hiển thị sau khi đăng nhập -->
                            <li class="nav-item">
                                <a class="nav-link" asp-controller="Sports" asp-action="Index">
                                    <i class="bi bi-dribbble"></i> Môn thể thao
                                </a>
                            </li>

                            <!-- Giải đấu - hiển thị sau khi đăng nhập -->
                            <li class="nav-item">
                                <a class="nav-link" asp-controller="Tournament" asp-action="Index">
                                    <i class="bi bi-trophy"></i> Giải đấu
                                </a>
                            </li>

                            @if (User.IsInRole(WebQuanLyGiaiDau_NhomTD.Models.UserModel.SD.Role_Admin))
                            {
                                <!-- Admin-specific navigation -->
                                <li class="nav-item dropdown">
                                    <a class="nav-link dropdown-toggle" href="#" id="adminDropdown" data-bs-toggle="dropdown">
                                        <i class="bi bi-gear"></i> Quản lý
                                    </a>
                                    <ul class="dropdown-menu" aria-labelledby="adminDropdown">
                                        <li>
                                            <a class="dropdown-item" asp-controller="Teams" asp-action="Index">
                                                <i class="bi bi-people-fill"></i> Đội bóng
                                            </a>
                                        </li>
                                        <li>
                                            <a class="dropdown-item" asp-controller="Players" asp-action="Index">
                                                <i class="bi bi-person-fill"></i> Cầu thủ
                                            </a>
                                        </li>
                                        <li>
                                            <a class="dropdown-item" asp-controller="Match" asp-action="Index">
                                                <i class="bi bi-calendar-event"></i> Trận đấu
                                            </a>
                                        </li>

                                        <li>
                                            <a class="dropdown-item" asp-controller="News" asp-action="Index">
                                                <i class="bi bi-newspaper"></i> Quản lý tin tức
                                            </a>
                                        </li>
                                        <li><hr class="dropdown-divider"></li>
                                        <li>
                                            <a class="dropdown-item" asp-controller="Tournament" asp-action="ManageRegistrations">
                                                <i class="bi bi-person-check"></i> Đăng ký người dùng
                                            </a>
                                        </li>
                                        <li>
                                            <a class="dropdown-item" asp-controller="Tournament" asp-action="ManageTeamRegistrations">
                                                <i class="bi bi-people-check"></i> Đăng ký đội
                                            </a>
                                        </li>
                                    </ul>
                                </li>
                            }

                            @if (User.IsInRole(WebQuanLyGiaiDau_NhomTD.Models.UserModel.SD.Role_User))
                            {
                                <!-- User-specific navigation -->
                                <li class="nav-item dropdown">
                                    <a class="nav-link dropdown-toggle" href="#" id="userDropdown" data-bs-toggle="dropdown">
                                        <i class="bi bi-person-gear"></i> Quản lý
                                    </a>
                                    <ul class="dropdown-menu" aria-labelledby="userDropdown">
                                        <li>
                                            <a class="dropdown-item" asp-controller="Teams" asp-action="Index">
                                                <i class="bi bi-people-fill"></i> Đội bóng
                                            </a>
                                        </li>
                                        <li>
                                            <a class="dropdown-item" asp-controller="Tournament" asp-action="Index">
                                                <i class="bi bi-trophy"></i> Giải đấu
                                            </a>
                                        </li>
                                        <li>
                                            <a class="dropdown-item" asp-controller="Tournament" asp-action="MyRegistrations">
                                                <i class="bi bi-list-check"></i> Đăng ký giải đấu
                                            </a>
                                        </li>
                                        <li><hr class="dropdown-divider"></li>
                                        <li>
                                            <a class="dropdown-item" asp-controller="Home" asp-action="UserGuide">
                                                <i class="bi bi-question-circle"></i> Hướng dẫn sử dụng
                                            </a>
                                        </li>
                                    </ul>
                                </li>
                            }
                        }
                        else
                        {
                            <!-- Navigation for non-authenticated users -->
                            <li class="nav-item">
                                <a class="nav-link" asp-controller="Home" asp-action="Privacy">
                                    <i class="bi bi-shield-check"></i> Chính sách
                                </a>
                            </li>
                        }
                    </ul>

                    <div class="d-flex">
                        <partial name="_LoginPartial" />
                    </div>
                </div>
            </div>
        </nav>
    </header>

    <div class="container mt-4">
        <main role="main" class="pb-3">
            @RenderBody()
        </main>
    </div>

    <footer class="sports-footer mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h5 class="mb-3">Hệ Thống Quản Lý Giải Đấu</h5>
                    <p class="mb-0">Nền tảng quản lý giải đấu thể thao toàn diện, dễ sử dụng và hiệu quả.</p>
                </div>
                <div class="col-md-3">
                    <h5 class="mb-3">Liên kết</h5>
                    <ul class="list-unstyled">
                        <li><a asp-controller="Home" asp-action="Index"><i class="bi bi-house-door me-2"></i>Trang chủ</a></li>
                        <li><a asp-controller="News" asp-action="Index"><i class="bi bi-newspaper me-2"></i>Tin tức thể thao</a></li>
                        <li><a asp-controller="Rules" asp-action="Index"><i class="bi bi-book me-2"></i>Luật thi đấu</a></li>
                        <li><a asp-controller="Home" asp-action="Privacy"><i class="bi bi-shield-check me-2"></i>Chính sách</a></li>
                    </ul>
                </div>
                <div class="col-md-3">
                    <h5 class="mb-3">Liên hệ</h5>
                    <ul class="list-unstyled">
                        <li><i class="bi bi-envelope me-2"></i><EMAIL></li>
                        <li><i class="bi bi-telephone me-2"></i>(+84) 123 456 789</li>
                    </ul>
                </div>
            </div>
            <hr class="mt-4 mb-4" style="border-color: rgba(255,255,255,0.1);">
            <div class="text-center">
                &copy; 2025 - Hệ Thống Quản Lý Giải Đấu - Mọi quyền được bảo lưu
            </div>
        </div>
    </footer>

    <script src="~/lib/jquery/dist/jquery.min.js"></script>
    <script src="~/lib/bootstrap/dist/js/bootstrap.bundle.min.js"></script>
    <script src="~/js/site.js" asp-append-version="true"></script>
    @await RenderSectionAsync("Scripts", required: false)

</body>
</html>

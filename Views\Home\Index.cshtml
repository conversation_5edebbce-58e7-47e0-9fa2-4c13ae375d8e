﻿@{
    ViewData["Title"] = "Trang chủ";
}

<div class="sports-hero">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-6 text-center text-lg-start" data-animation="animate-fade-in-left">
                <h1 class="mb-4">
                    <span class="text-warning">Hệ Thống</span> Quản Lý Giải Đấu
                </h1>
                <p class="hero-subtitle">Nền tảng quản lý giải đấu thể thao toàn diện, dễ sử dụng và hiệu quả cho mọi môn thể thao</p>

                @if (User.Identity == null || !User.Identity.IsAuthenticated)
                {
                    <div class="hero-buttons">
                        <a asp-area="Identity" asp-page="/Account/Login" class="btn btn-sports-primary btn-sports-lg">
                            <i class="bi bi-box-arrow-in-right"></i><PERSON><PERSON><PERSON> nh<PERSON>
                        </a>
                        <a asp-area="Identity" asp-page="/Account/Register" class="btn btn-sports-secondary btn-sports-lg">
                            <i class="bi bi-person-plus"></i>Đăng ký
                        </a>
                    </div>
                }
                else
                {
                    <div class="hero-buttons">
                        <a asp-controller="Sports" asp-action="Index" class="btn btn-sports-primary btn-sports-lg">
                            <i class="bi bi-dribbble"></i>Khám phá môn thể thao
                        </a>
                        <a asp-controller="Tournament" asp-action="Index" class="btn btn-sports-secondary btn-sports-lg">
                            <i class="bi bi-trophy"></i>Xem giải đấu
                        </a>
                    </div>
                }
            </div>
            <div class="col-lg-6 d-none d-lg-block" data-animation="animate-fade-in-right" data-delay="0.3">
                <div class="logo-container">
                    <img src="/images/Logomain.png" alt="Logo" class="img-fluid main-logo animate-float">
                </div>
            </div>
        </div>
    </div>
</div>

<section class="features-section py-5">
    <div class="container">
        <div class="text-center mb-5" data-animation="animate-fade-in-up">
            <h2 class="mb-3">Tính Năng Nổi Bật</h2>
            <p class="text-muted">Khám phá những tính năng giúp quản lý giải đấu thể thao hiệu quả</p>
        </div>

        <div class="row g-4">
            <div class="col-lg-4 col-md-6" data-animation="animate-fade-in-up" data-delay="0.1">
                <div class="sports-card feature-card h-100">
                    <div class="card-body">
                        <div class="card-icon">
                            <i class="bi bi-trophy"></i>
                        </div>
                        <h3 class="card-title">Quản lý giải đấu</h3>
                        <p class="card-text">Tạo và quản lý các giải đấu thể thao một cách dễ dàng với hệ thống lịch thi đấu thông minh</p>
                        <div class="mt-auto">
                            <a href="#" class="btn btn-sports-outline btn-sports-sm">Tìm hiểu thêm</a>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-4 col-md-6" data-animation="animate-fade-in-up" data-delay="0.2">
                <div class="sports-card feature-card h-100">
                    <div class="card-body">
                        <div class="card-icon">
                            <i class="bi bi-people-fill"></i>
                        </div>
                        <h3 class="card-title">Quản lý đội bóng</h3>
                        <p class="card-text">Theo dõi thông tin về các đội bóng, cầu thủ và thành tích trong các giải đấu</p>
                        <div class="mt-auto">
                            <a href="#" class="btn btn-sports-outline btn-sports-sm">Tìm hiểu thêm</a>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-4 col-md-6" data-animation="animate-fade-in-up" data-delay="0.3">
                <div class="sports-card feature-card h-100">
                    <div class="card-body">
                        <div class="card-icon">
                            <i class="bi bi-clipboard-check"></i>
                        </div>
                        <h3 class="card-title">Đăng ký tham gia</h3>
                        <p class="card-text">Đăng ký tham gia các giải đấu một cách nhanh chóng và theo dõi trạng thái đăng ký</p>
                        <div class="mt-auto">
                            <a href="#" class="btn btn-sports-outline btn-sports-sm">Tìm hiểu thêm</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Tin tức thể thao -->
<section class="news-section py-5 bg-light">
    <div class="container">
        <div class="text-center mb-5" data-animation="animate-fade-in-up">
            <h2 class="mb-3">Tin Tức Thể Thao</h2>
            <p class="text-muted">Cập nhật những tin tức mới nhất về thể thao</p>
        </div>

        <!-- Tin tức nổi bật -->
        <div class="featured-news mb-5" data-animation="animate-fade-in-up" data-delay="0.2">
            <div class="row" id="featuredNewsContainer">
                <!-- Dữ liệu sẽ được tải bằng AJAX -->
                <div class="col-12 text-center">
                    <div class="loading-spinner"></div>
                    <p class="mt-3 text-muted">Đang tải tin tức...</p>
                </div>
            </div>
        </div>

        <!-- Tin tức mới nhất -->
        <div data-animation="animate-fade-in-up" data-delay="0.3">
            <h3 class="mb-4">Tin tức mới nhất</h3>
            <div class="row g-4" id="latestNewsContainer">
                <!-- Dữ liệu sẽ được tải bằng AJAX -->
                <div class="col-12 text-center">
                    <div class="loading-spinner"></div>
                    <p class="mt-3 text-muted">Đang tải tin tức...</p>
                </div>
            </div>
        </div>

        <div class="text-center mt-5" data-animation="animate-fade-in-up" data-delay="0.4">
            <a asp-controller="News" asp-action="Index" class="btn btn-sports-primary btn-sports-lg">
                <i class="bi bi-newspaper"></i>Xem tất cả tin tức
            </a>
        </div>
    </div>
</section>

<section class="sports-section py-5">
    <div class="container">
        <div class="text-center mb-5" data-animation="animate-fade-in-up">
            <h2 class="mb-3">Các Môn Thể Thao</h2>
            <p class="text-muted">Hệ thống hỗ trợ đa dạng các môn thể thao phổ biến</p>
        </div>

        <div class="row g-4 justify-content-center">
            <div class="col-lg-3 col-md-4 col-6 text-center" data-animation="animate-scale-in" data-delay="0.1">
                <div class="sport-icon-container hover-lift">
                    <div class="sport-icon-bg">
                        <img src="/images/basketball-icon.png" alt="Bóng rổ" class="img-fluid sport-icon">
                    </div>
                    <h4 class="mt-3">Bóng rổ</h4>
                </div>
            </div>
            <div class="col-lg-3 col-md-4 col-6 text-center" data-animation="animate-scale-in" data-delay="0.2">
                <div class="sport-icon-container hover-lift">
                    <div class="sport-icon-bg">
                        <img src="/images/football_icon.jpeg" alt="Bóng đá" class="img-fluid sport-icon">
                    </div>
                    <h4 class="mt-3">Bóng đá</h4>
                </div>
            </div>
            <div class="col-lg-3 col-md-4 col-6 text-center" data-animation="animate-scale-in" data-delay="0.3">
                <div class="sport-icon-container hover-lift">
                    <div class="sport-icon-bg">
                        <i class="bi bi-dribbble sport-icon-fallback"></i>
                    </div>
                    <h4 class="mt-3">Các môn khác</h4>
                </div>
            </div>
        </div>
    </div>
</section>



<!-- AJAX script để tải tin tức -->
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Tải tin tức nổi bật
        fetch('/News/GetFeaturedNews')
            .then(response => response.json())
            .then(data => {
                const featuredNewsContainer = document.getElementById('featuredNewsContainer');
                featuredNewsContainer.innerHTML = '';

                if (data.length > 0) {
                    // Hiển thị tin nổi bật đầu tiên lớn hơn
                    const featuredHtml = `
                        <div class="col-lg-6 mb-4" data-animation="animate-fade-in-left">
                            <div class="sports-card sports-card-featured featured-news-card">
                                <img src="${data[0].imageUrl || '/images/news/default.jpg'}" alt="${data[0].title}" class="card-img-top">
                                <div class="card-body">
                                    <div class="card-badge">
                                        <i class="bi bi-star-fill me-1"></i>Nổi bật
                                    </div>
                                    <div class="news-date mb-2">
                                        <i class="bi bi-calendar-event me-1"></i>${data[0].publishDate}
                                    </div>
                                    <h3 class="card-title">${data[0].title}</h3>
                                    <p class="card-text">${data[0].summary}</p>
                                    <a href="/News/Details/${data[0].newsId}" class="btn btn-sports-outline btn-sports-sm">Đọc tiếp</a>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-6">
                            <div class="row">`;

                    // Hiển thị các tin nổi bật còn lại nhỏ hơn
                    let otherFeaturedHtml = '';
                    for (let i = 1; i < data.length && i < 3; i++) {
                        otherFeaturedHtml += `
                            <div class="col-md-6 mb-4" data-animation="animate-fade-in-right" data-delay="0.${i}">
                                <div class="sports-card news-card">
                                    <img src="${data[i].imageUrl || '/images/news/default.jpg'}" alt="${data[i].title}" class="card-img-top">
                                    <div class="card-body">
                                        <div class="news-date mb-2">
                                            <i class="bi bi-calendar-event me-1"></i>${data[i].publishDate}
                                        </div>
                                        <h5 class="card-title">${data[i].title}</h5>
                                        <a href="/News/Details/${data[i].newsId}" class="btn btn-sports-outline btn-sports-sm">Đọc tiếp</a>
                                    </div>
                                </div>
                            </div>`;
                    }

                    featuredNewsContainer.innerHTML = featuredHtml + otherFeaturedHtml + '</div></div>';
                } else {
                    featuredNewsContainer.innerHTML = '<div class="col-12 text-center"><p class="text-muted">Không có tin tức nổi bật.</p></div>';
                }
            })
            .catch(error => {
                console.error('Lỗi khi tải tin tức nổi bật:', error);
                document.getElementById('featuredNewsContainer').innerHTML =
                    '<div class="col-12 text-center"><p class="text-danger">Đã xảy ra lỗi khi tải tin tức nổi bật.</p></div>';
            });

        // Tải tin tức mới nhất
        fetch('/News/GetLatestNews')
            .then(response => response.json())
            .then(data => {
                const latestNewsContainer = document.getElementById('latestNewsContainer');
                latestNewsContainer.innerHTML = '';

                if (data.length > 0) {
                    let latestHtml = '';
                    data.forEach((news, index) => {
                        latestHtml += `
                            <div class="col-lg-4 col-md-6 mb-4" data-animation="animate-fade-in-up" data-delay="0.${index + 1}">
                                <div class="sports-card news-card">
                                    <img src="${news.imageUrl || '/images/news/default.jpg'}" alt="${news.title}" class="card-img-top">
                                    <div class="card-body">
                                        <div class="news-date mb-2">
                                            <i class="bi bi-calendar-event me-1"></i>${news.publishDate}
                                            <span class="ms-2"><i class="bi bi-eye me-1"></i>${news.viewCount}</span>
                                        </div>
                                        <h5 class="card-title">${news.title}</h5>
                                        <p class="card-text">${news.summary}</p>
                                        <a href="/News/Details/${news.newsId}" class="btn btn-sports-outline btn-sports-sm">Đọc tiếp</a>
                                    </div>
                                </div>
                            </div>`;
                    });
                    latestNewsContainer.innerHTML = latestHtml;
                } else {
                    latestNewsContainer.innerHTML = '<div class="col-12 text-center"><p class="text-muted">Không có tin tức mới.</p></div>';
                }
            })
            .catch(error => {
                console.error('Lỗi khi tải tin tức mới nhất:', error);
                document.getElementById('latestNewsContainer').innerHTML =
                    '<div class="col-12 text-center"><p class="text-danger">Đã xảy ra lỗi khi tải tin tức mới nhất.</p></div>';
            });
    });
</script>

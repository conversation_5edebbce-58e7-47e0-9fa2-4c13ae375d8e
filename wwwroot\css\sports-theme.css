/* Sports Theme CSS - Modern, Dynamic, User-friendly */

:root {
    /* Main color palette */
    --primary-color: #0066cc;       /* Blue - Primary brand color */
    --secondary-color: #ff6600;     /* Orange - Energy and excitement */
    --accent-color: #33cc33;        /* Green - Success and action */
    --dark-color: #1a1a2e;          /* Dark blue - For backgrounds */
    --light-color: #f8f9fa;         /* Light gray - For backgrounds */
    --warning-color: #ffcc00;       /* Yellow - Warnings and highlights */
    --danger-color: #ff3333;        /* Red - Errors and important actions */
    
    /* Text colors */
    --text-dark: #212529;
    --text-light: #f8f9fa;
    --text-muted: #6c757d;
    
    /* Gradients */
    --primary-gradient: linear-gradient(135deg, #0066cc, #0099ff);
    --secondary-gradient: linear-gradient(135deg, #ff6600, #ff9933);
    --dark-gradient: linear-gradient(135deg, #1a1a2e, #16213e);
    
    /* Shadows */
    --card-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    --hover-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    --button-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    
    /* Transitions */
    --transition-fast: all 0.2s ease;
    --transition-normal: all 0.3s ease;
    --transition-slow: all 0.5s ease;
    
    /* Border radius */
    --border-radius-sm: 4px;
    --border-radius-md: 8px;
    --border-radius-lg: 16px;
    --border-radius-xl: 24px;
    --border-radius-circle: 50%;
}

/* Base styles */
body {
    font-family: 'Roboto', 'Segoe UI', sans-serif;
    color: var(--text-dark);
    background-color: var(--light-color);
    line-height: 1.6;
    overflow-x: hidden;
    margin: 0;
    padding: 0;
}

/* Navbar styles */
.sports-navbar {
    background: var(--dark-gradient);
    padding: 0.8rem 1rem;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    transition: var(--transition-normal);
}

.sports-navbar .navbar-brand {
    font-weight: 700;
    font-size: 1.5rem;
    color: var(--text-light);
    display: flex;
    align-items: center;
}

.sports-navbar .navbar-brand i {
    color: var(--warning-color);
    margin-right: 0.5rem;
    font-size: 1.8rem;
}

.sports-navbar .nav-link {
    color: rgba(255, 255, 255, 0.85);
    font-weight: 500;
    padding: 0.5rem 1rem;
    border-radius: var(--border-radius-md);
    transition: var(--transition-fast);
    margin: 0 0.2rem;
}

.sports-navbar .nav-link:hover {
    color: #fff;
    background-color: rgba(255, 255, 255, 0.1);
    transform: translateY(-2px);
}

.sports-navbar .nav-link.active {
    color: #fff;
    background-color: rgba(255, 255, 255, 0.15);
}

.sports-navbar .dropdown-menu {
    background-color: var(--dark-color);
    border: none;
    border-radius: var(--border-radius-md);
    box-shadow: var(--card-shadow);
    padding: 0.5rem;
    margin-top: 0.5rem;
}

.sports-navbar .dropdown-item {
    color: var(--text-light);
    border-radius: var(--border-radius-sm);
    padding: 0.6rem 1rem;
    transition: var(--transition-fast);
}

.sports-navbar .dropdown-item:hover {
    background-color: rgba(255, 255, 255, 0.1);
    color: #fff;
}

.sports-navbar .dropdown-item i {
    margin-right: 0.5rem;
}

/* Button styles */
.btn-sports-primary {
    background: var(--primary-gradient);
    color: white;
    border: none;
    border-radius: var(--border-radius-md);
    padding: 0.6rem 1.5rem;
    font-weight: 600;
    transition: var(--transition-normal);
    box-shadow: var(--button-shadow);
}

.btn-sports-primary:hover {
    transform: translateY(-3px);
    box-shadow: var(--hover-shadow);
    color: white;
}

.btn-sports-secondary {
    background: var(--secondary-gradient);
    color: white;
    border: none;
    border-radius: var(--border-radius-md);
    padding: 0.6rem 1.5rem;
    font-weight: 600;
    transition: var(--transition-normal);
    box-shadow: var(--button-shadow);
}

.btn-sports-secondary:hover {
    transform: translateY(-3px);
    box-shadow: var(--hover-shadow);
    color: white;
}

.btn-sports-outline {
    background: transparent;
    color: var(--primary-color);
    border: 2px solid var(--primary-color);
    border-radius: var(--border-radius-md);
    padding: 0.6rem 1.5rem;
    font-weight: 600;
    transition: var(--transition-normal);
}

.btn-sports-outline:hover {
    background-color: var(--primary-color);
    color: white;
    transform: translateY(-3px);
    box-shadow: var(--button-shadow);
}

/* Card styles */
.sports-card {
    background-color: white;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--card-shadow);
    transition: var(--transition-normal);
    overflow: hidden;
    height: 100%;
    border: none;
}

.sports-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--hover-shadow);
}

.sports-card .card-img-top {
    height: 180px;
    object-fit: cover;
}

.sports-card .card-body {
    padding: 1.5rem;
}

.sports-card .card-title {
    font-weight: 700;
    margin-bottom: 1rem;
    color: var(--dark-color);
}

/* Hero section */
.sports-hero {
    background: var(--primary-gradient);
    color: white;
    padding: 5rem 0;
    position: relative;
    overflow: hidden;
}

.sports-hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: url('/images/pattern.png');
    background-size: cover;
    opacity: 0.1;
    z-index: 0;
}

.sports-hero .container {
    position: relative;
    z-index: 1;
}

.sports-hero h1 {
    font-weight: 800;
    font-size: 3.5rem;
    margin-bottom: 1.5rem;
}

.sports-hero p {
    font-size: 1.25rem;
    max-width: 600px;
    margin: 0 auto 2rem;
}

/* Footer */
.sports-footer {
    background: var(--dark-gradient);
    color: var(--text-light);
    padding: 2rem 0;
    margin-top: 3rem;
}

.sports-footer a {
    color: rgba(255, 255, 255, 0.8);
    transition: var(--transition-fast);
}

.sports-footer a:hover {
    color: white;
    text-decoration: none;
}

/* Animations */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.animate-fade-in {
    animation: fadeIn 0.5s ease forwards;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .sports-hero h1 {
        font-size: 2.5rem;
    }
    
    .sports-navbar .navbar-brand {
        font-size: 1.2rem;
    }
}
